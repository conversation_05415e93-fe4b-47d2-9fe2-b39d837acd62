#!/usr/bin/env python3
import json
import time
import logging
import paho.mqtt.client as mqtt

# =============================
# KONFIGURACE
# =============================
MQTT_HOST = "localhost"
MQTT_PORT = 1884
MQTT_CLIENT_ID = "b-003-simulator-local"
PUBLISH_TOPIC = "devices/b-003/responses/test"

# =============================
# LOGGING
# =============================
logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(message)s")
logger = logging.getLogger(__name__)

# =============================
# HLAVNÍ KÓD
# =============================
def main():
    client = mqtt.Client(client_id=MQTT_CLIENT_ID)
    client.connect(MQTT_HOST, MQTT_PORT, keepalive=60)
    
    counter = 0
    while True:
        message = {
            "msg_id": counter,
            "status": "ok",
            "value": counter * 2
        }
        payload = json.dumps(message)
        client.publish(PUBLISH_TOPIC, payload)
        logger.info(f"Published: {message}")
        counter += 1
        time.sleep(2)  # každé 2 sekundy nová zpráva

if __name__ == "__main__":
    main()

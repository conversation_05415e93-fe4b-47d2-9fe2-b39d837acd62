#!/usr/bin/env python3
import json
import logging
import paho.mqtt.client as mqtt

# =============================
# KONFIGURACE
# =============================
MQTT_HOST = "localhost"
MQTT_PORT = 1884
MQTT_CLIENT_ID = "b-003-listener-local"
RESPONSE_TOPIC = "devices/b-003/responses/+"

# =============================
# LOGGING
# =============================
logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(message)s")
logger = logging.getLogger(__name__)

# =============================
# CALLBACKY
# =============================
def on_connect(client, userdata, flags, rc):
    if rc == 0:
        logger.info("Connected to local MQTT broker")
        client.subscribe(RESPONSE_TOPIC)
        logger.info(f"Subscribed to {RESPONSE_TOPIC}")
    else:
        logger.error(f"Connection failed with code {rc}")

def on_message(client, userdata, msg):
    try:
        payload = json.loads(msg.payload.decode())
        logger.info(f"Received on {msg.topic}: {payload}")
    except Exception as e:
        logger.error(f"Error decoding message: {e}")

# =============================
# HLAVNÍ KÓD
# =============================
def main():
    client = mqtt.Client(client_id=MQTT_CLIENT_ID)
    client.on_connect = on_connect
    client.on_message = on_message
    client.connect(MQTT_HOST, MQTT_PORT, keepalive=60)
    client.loop_forever()

if __name__ == "__main__":
    main()

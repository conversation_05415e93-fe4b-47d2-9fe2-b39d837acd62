Elektro - priorita: velká
devices/{clientid}/commands/electronic/unlock
{
    "request_uuid": "",
    "section_id": 1,
}
devices/{clientid}/responses/electronic/unlock
{
    "success": true,
    "section_id": 3,
    "message": "Section 3 opened successfully",
    "request_uuid": "abc555"
}



devices/{clientid}/commands/electronic/section_open
{
    "request_uuid": "",
    "section_id": 1
}

devices/{clientid}/responses/electronic/section_open
{
    "success": true,
    "message": "door was succesfully opened",
    "request_uuid": "fads23",
    "section_id": 1
}



devices/{clientid}/commands/electronic/door_state
{
    "request_uuid": "",
    "section_id": 1
}

devices/{clientid}/responses/electronic/door_state
{
    "success": true,
    "message": "door was succesfully cehcked",
    "request_uuid": "fads23",
    "section_id": 1
}



devices/{clientid}/commands/system/reboot_device
{
    "request_uuid": "abc555"
}

devices/{clientid}/responses/system/reboot
{
    "success": true,
    "message": "Rebooting",
    "request_uuid": "abc555"
}



devices/{clientid}/commands/sale/edit_reservation
{
    "request_uuid": "abc555",
    "product_uuid": "fdsa43" // optional
    "section_id": 3,    //optional
    "status": 0,    // optional, change status to ...
    "price": 34,    // optional, change price to ...
}// Either "product_uuid" or "section_id" must be entered

devices/{clientid}/responses/sale/edit_reservations
{
    "success": true,
    "message": "Product edited succesfully",
    "request_uuid": "abc555",
    "product_uuid": "fdsa43", //uuid of edited product
    "sections_id": 3 //seciton_id of edited product
    "status": 0, // status after changes
    "price": 34, //price after reservation
}

devices/{clientid}/commands/sale/reserve_product
{
    "request_uuid": "abc555",
    "product_uuid": "fdsa43" // optional
    "section_id": 3    //optional
}// Either "product_uuid" or "section_id" must be entered

devices/{clientid}/responses/sale/reserve_product
{
    "success": true,
    "message": "Product reserved succesfully",
    "request_uuid": "abc555",
    "product_uuid": "fdsa43", //uuid of edited product
    "sections_id": 3 //seciton_id of edited product
    "status": 0, //  of reserved product
    "price": 34, // price of reserved 
    "reservation_pin": 123456  // generated reservation pin for reserved product
}

devices/{clientid}/commands/sale/unreserve_product
{
    "request_uuid": "abc555",
    "product_uuid": "fdsa43" // optional
    "section_id": 3    //optional
    "reservation_pin": 123456
}// Either "product_uuid" or "section_id" or "reservation_pin" must be entered



devices/{clientid}/responses/sale/unreserve_product
{
    "success": true,
    "message": "Product unreserved succesfully",
    "request_uuid": "abc555",
    "product_uuid": "fdsa43", //uuid of product
    "sections_id": 3 //seciton_id of product
    "status": 0, //  status of product
    "price": 34, // price of product
}

Storage - priorita: malá
devices/{clientid}/commands/storage/edit_reservation
{
    "success": true,
    "message": "Storage edited succesfully",
    "request_uuid": "abc555",
    "reservation_uuid": "fdsa43", //uuid of reservation
    "sections_id": 3 //seciton_id of reservaiton
    "status": 0, // status after changes
    "reservation_pin": 123456 // current reservation pin of reservation
}

devices/{clientid}/responses/storage/edit_reservations
{
    "request_uuid": "abc555",
    "reservation_uuid": "fdsa43" // optional
    "reservation_pin": 123456 //optional
    "section_id": 3,    //optional
    "status": 0,    // optional, change status to ...
}// Either "product_uuid" or "section_id"or "reservation_pin" must be entered